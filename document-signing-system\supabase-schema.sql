-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON> custom types
CREATE TYPE document_status AS ENUM ('pending', 'in_review', 'signed', 'rejected', 'needs_revision');
CREATE TYPE urgency_level AS ENUM ('urgent', 'neutral', 'none');
CREATE TYPE user_role AS ENUM ('user', 'admin');

-- Create profiles table (extends auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  role user_role DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create documents table
CREATE TABLE documents (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  subject TEXT NOT NULL,
  description TEXT,
  urgency urgency_level DEFAULT 'none',
  status document_status DEFAULT 'pending',
  original_filename TEXT NOT NULL,
  original_file_size BIGINT,
  original_mime_type TEXT,
  google_drive_file_id TEXT,
  google_drive_folder_id TEXT,
  signed_filename TEXT,
  signed_file_size BIGINT,
  signed_mime_type TEXT,
  signed_google_drive_file_id TEXT,
  assigned_admin_id UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  signed_at TIMESTAMP WITH TIME ZONE
);

-- Create document_feedback table
CREATE TABLE document_feedback (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  document_id UUID REFERENCES documents(id) ON DELETE CASCADE NOT NULL,
  admin_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  feedback_text TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create document_history table for tracking status changes
CREATE TABLE document_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  document_id UUID REFERENCES documents(id) ON DELETE CASCADE NOT NULL,
  previous_status document_status,
  new_status document_status NOT NULL,
  changed_by UUID REFERENCES profiles(id) NOT NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_assigned_admin ON documents(assigned_admin_id);
CREATE INDEX idx_documents_created_at ON documents(created_at);
CREATE INDEX idx_document_feedback_document_id ON document_feedback(document_id);
CREATE INDEX idx_document_history_document_id ON document_history(document_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_history ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Documents policies
CREATE POLICY "Users can view own documents" ON documents
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert own documents" ON documents
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own documents" ON documents
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Admins can view all documents" ON documents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Document feedback policies
CREATE POLICY "Users can view feedback on own documents" ON document_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM documents 
            WHERE id = document_feedback.document_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all feedback" ON document_feedback
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Document history policies
CREATE POLICY "Users can view history of own documents" ON document_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM documents 
            WHERE id = document_history.document_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can view all document history" ON document_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Authenticated users can insert document history" ON document_history
    FOR INSERT WITH CHECK (changed_by = auth.uid());

-- Function to automatically create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
