import { google } from 'googleapis'

const SCOPES = ['https://www.googleapis.com/auth/drive.file']

export class GoogleDriveService {
  private drive: any

  constructor() {
    const auth = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      'urn:ietf:wg:oauth:2.0:oob'
    )

    auth.setCredentials({
      refresh_token: process.env.GOOGLE_REFRESH_TOKEN,
    })

    this.drive = google.drive({ version: 'v3', auth })
  }

  async createFolder(name: string, parentFolderId?: string) {
    try {
      const fileMetadata = {
        name: name,
        mimeType: 'application/vnd.google-apps.folder',
        parents: parentFolderId ? [parentFolderId] : [process.env.GOOGLE_DRIVE_FOLDER_ID],
      }

      const response = await this.drive.files.create({
        resource: fileMetadata,
        fields: 'id',
      })

      return response.data.id
    } catch (error) {
      console.error('Error creating folder:', error)
      throw error
    }
  }

  async uploadFile(
    fileName: string,
    fileBuffer: Buffer,
    mimeType: string,
    folderId?: string
  ) {
    try {
      const fileMetadata = {
        name: fileName,
        parents: [folderId || process.env.GOOGLE_DRIVE_FOLDER_ID],
      }

      const media = {
        mimeType: mimeType,
        body: fileBuffer,
      }

      const response = await this.drive.files.create({
        resource: fileMetadata,
        media: media,
        fields: 'id,name,webViewLink,webContentLink',
      })

      return response.data
    } catch (error) {
      console.error('Error uploading file:', error)
      throw error
    }
  }

  async downloadFile(fileId: string) {
    try {
      const response = await this.drive.files.get({
        fileId: fileId,
        alt: 'media',
      })

      return response.data
    } catch (error) {
      console.error('Error downloading file:', error)
      throw error
    }
  }

  async getFileMetadata(fileId: string) {
    try {
      const response = await this.drive.files.get({
        fileId: fileId,
        fields: 'id,name,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink',
      })

      return response.data
    } catch (error) {
      console.error('Error getting file metadata:', error)
      throw error
    }
  }

  async deleteFile(fileId: string) {
    try {
      await this.drive.files.delete({
        fileId: fileId,
      })
      return true
    } catch (error) {
      console.error('Error deleting file:', error)
      throw error
    }
  }
}
