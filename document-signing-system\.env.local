# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Google Drive API Configuration
GOOGLE_CLIENT_ID=801977315256-8cim3rmn4q8d04pipg4so2v7kkunir1i.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REFRESH_TOKEN=your_google_refresh_token
GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Application Configuration
ADMIN_EMAIL=<EMAIL>
