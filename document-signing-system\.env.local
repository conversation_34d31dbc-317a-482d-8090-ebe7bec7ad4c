# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Google Drive API Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REFRESH_TOKEN=your_google_refresh_token_here
GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id_here

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
# For production, change to: https://your-domain.vercel.app
NEXTAUTH_SECRET=your_random_secret_key_here

# Application Configuration
ADMIN_EMAIL=<EMAIL>
