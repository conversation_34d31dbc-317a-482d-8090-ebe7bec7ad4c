export type DocumentStatus = 'pending' | 'in_review' | 'signed' | 'rejected' | 'needs_revision'
export type UrgencyLevel = 'urgent' | 'neutral' | 'none'
export type UserRole = 'user' | 'admin'

export interface Profile {
  id: string
  email: string
  full_name?: string
  role: User<PERSON><PERSON>
  created_at: string
  updated_at: string
}

export interface Document {
  id: string
  user_id: string
  title: string
  subject: string
  description?: string
  urgency: UrgencyLevel
  status: DocumentStatus
  original_filename: string
  original_file_size?: number
  original_mime_type?: string
  google_drive_file_id?: string
  google_drive_folder_id?: string
  signed_filename?: string
  signed_file_size?: number
  signed_mime_type?: string
  signed_google_drive_file_id?: string
  assigned_admin_id?: string
  created_at: string
  updated_at: string
  signed_at?: string
  // Relations
  user?: Profile
  assigned_admin?: Profile
  feedback?: DocumentFeedback[]
  history?: DocumentHistory[]
}

export interface DocumentFeedback {
  id: string
  document_id: string
  admin_id: string
  feedback_text: string
  created_at: string
  // Relations
  admin?: Profile
  document?: Document
}

export interface DocumentHistory {
  id: string
  document_id: string
  previous_status?: DocumentStatus
  new_status: DocumentStatus
  changed_by: string
  notes?: string
  created_at: string
  // Relations
  changed_by_user?: Profile
  document?: Document
}

export interface CreateDocumentRequest {
  title: string
  subject: string
  description?: string
  urgency: UrgencyLevel
  file: File
}

export interface UpdateDocumentRequest {
  id: string
  title?: string
  subject?: string
  description?: string
  urgency?: UrgencyLevel
  status?: DocumentStatus
  assigned_admin_id?: string
}

export interface CreateFeedbackRequest {
  document_id: string
  feedback_text: string
}

export interface GoogleDriveFile {
  id: string
  name: string
  webViewLink?: string
  webContentLink?: string
  mimeType?: string
  size?: string
  createdTime?: string
  modifiedTime?: string
}

export interface UploadResponse {
  success: boolean
  document?: Document
  error?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}
