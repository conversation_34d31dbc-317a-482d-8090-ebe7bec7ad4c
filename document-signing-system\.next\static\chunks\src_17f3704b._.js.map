{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Source%20Code/c/document-signing-system/src/lib/supabase.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\nimport { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n\nexport function createServerSupabaseClient() {\n  const cookieStore = cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n\nexport function createServiceSupabaseClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;;;AAMI;AANJ;AAAA;AACA;AACA;;;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;AAI3B;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE1B,OAAO,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,6HAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC;4BAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;+BAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;;gBAEjC,EAAE,UAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,iEAEvB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;AAEzC", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Source%20Code/c/document-signing-system/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createClient } from '@/lib/supabase'\nimport { Profile } from '@/types/database'\n\ninterface AuthContextType {\n  user: User | null\n  profile: Profile | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  isAdmin: boolean\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<Profile | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createClient()\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setUser(session?.user ?? null)\n      \n      if (session?.user) {\n        await fetchProfile(session.user.id)\n      }\n      \n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        \n        if (session?.user) {\n          await fetchProfile(session.user.id)\n        } else {\n          setProfile(null)\n        }\n        \n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        console.error('Error fetching profile:', error)\n        return\n      }\n\n      setProfile(data)\n    } catch (error) {\n      console.error('Error fetching profile:', error)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    const { error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName,\n        },\n      },\n    })\n    return { error }\n  }\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const isAdmin = profile?.role === 'admin'\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    isAdmin,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;wBACpD;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBAEzB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;oBACpC;oBAEA,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;wBACJ;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBAEzB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;oBACpC,OAAO;wBACL,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC;YACF;YAEA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;YACA,SAAS;gBACP,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,UAAU,CAAA,oBAAA,8BAAA,QAAS,IAAI,MAAK;IAElC,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GApGgB;KAAA;AAsGT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}