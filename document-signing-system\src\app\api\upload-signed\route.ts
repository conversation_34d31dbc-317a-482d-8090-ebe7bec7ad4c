import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { GoogleDriveService } from '@/lib/google-drive'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const documentId = formData.get('documentId') as string

    if (!file || !documentId) {
      return NextResponse.json(
        { error: 'Missing required fields: file, documentId' },
        { status: 400 }
      )
    }

    // Get the original document
    const { data: document, error: docError } = await supabase
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .single()

    if (docError || !document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Initialize Google Drive service
    const driveService = new GoogleDriveService()

    // Create signed documents folder in user's directory
    const userFolderName = `user_${document.user_id}`
    const userFolderId = await driveService.createFolder(userFolderName)
    const signedFolderId = await driveService.createFolder('signed_documents', userFolderId)

    // Upload signed file to Google Drive
    const driveFile = await driveService.uploadFile(
      file.name,
      buffer,
      file.type,
      signedFolderId
    )

    // Update document record with signed file information
    const { data: updatedDocument, error: updateError } = await supabase
      .from('documents')
      .update({
        signed_filename: file.name,
        signed_file_size: file.size,
        signed_mime_type: file.type,
        signed_google_drive_file_id: driveFile.id,
        status: 'signed',
        signed_at: new Date().toISOString(),
        assigned_admin_id: user.id
      })
      .eq('id', documentId)
      .select()
      .single()

    if (updateError) {
      console.error('Database update error:', updateError)
      return NextResponse.json(
        { error: 'Failed to update document record' },
        { status: 500 }
      )
    }

    // Create document history entry
    await supabase
      .from('document_history')
      .insert({
        document_id: documentId,
        previous_status: document.status,
        new_status: 'signed',
        changed_by: user.id,
        notes: 'Document signed and uploaded'
      })

    return NextResponse.json({
      success: true,
      document: updatedDocument,
      driveFile
    })

  } catch (error) {
    console.error('Upload signed document error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
