{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Source%20Code/c/document-signing-system/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport Link from 'next/link'\nimport { FileText, Shield, Users, CheckCircle } from 'lucide-react'\n\nexport default function Home() {\n  const { user, profile, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && user) {\n      if (profile?.role === 'admin') {\n        router.push('/admin')\n      } else {\n        router.push('/dashboard')\n      }\n    }\n  }, [user, profile, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"></div>\n      </div>\n    )\n  }\n\n  if (user) {\n    return null // Will redirect\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-50 to-white\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <FileText className=\"h-8 w-8 text-indigo-600\" />\n              <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                Document Signing System\n              </span>\n            </div>\n            <div className=\"flex space-x-4\">\n              <Link\n                href=\"/login\"\n                className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Sign In\n              </Link>\n              <Link\n                href=\"/register\"\n                className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Sign Up\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-extrabold text-gray-900 sm:text-5xl md:text-6xl\">\n            Streamline Your\n            <span className=\"text-indigo-600\"> Document Signing</span>\n          </h1>\n          <p className=\"mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl\">\n            A comprehensive system for uploading, reviewing, and signing documents with real-time status tracking and Google Drive integration.\n          </p>\n          <div className=\"mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8\">\n            <div className=\"rounded-md shadow\">\n              <Link\n                href=\"/register\"\n                className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10\"\n              >\n                Get Started\n              </Link>\n            </div>\n            <div className=\"mt-3 rounded-md shadow sm:mt-0 sm:ml-3\">\n              <Link\n                href=\"/login\"\n                className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10\"\n              >\n                Sign In\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        {/* Features */}\n        <div className=\"mt-20\">\n          <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4\">\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white mx-auto\">\n                <FileText className=\"h-6 w-6\" />\n              </div>\n              <h3 className=\"mt-6 text-lg font-medium text-gray-900\">Easy Upload</h3>\n              <p className=\"mt-2 text-base text-gray-500\">\n                Upload documents with subject, description, and urgency levels\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white mx-auto\">\n                <Shield className=\"h-6 w-6\" />\n              </div>\n              <h3 className=\"mt-6 text-lg font-medium text-gray-900\">Secure Storage</h3>\n              <p className=\"mt-2 text-base text-gray-500\">\n                Documents stored securely in Google Drive with proper access controls\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white mx-auto\">\n                <Users className=\"h-6 w-6\" />\n              </div>\n              <h3 className=\"mt-6 text-lg font-medium text-gray-900\">Admin Review</h3>\n              <p className=\"mt-2 text-base text-gray-500\">\n                Dedicated admin interface for reviewing and managing documents\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white mx-auto\">\n                <CheckCircle className=\"h-6 w-6\" />\n              </div>\n              <h3 className=\"mt-6 text-lg font-medium text-gray-900\">Real-time Status</h3>\n              <p className=\"mt-2 text-base text-gray-500\">\n                Track document status from upload to signing completion\n              </p>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-20\">\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center text-gray-500\">\n            <p>&copy; 2024 Document Signing System. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,MAAM;YACpB,IAAI,SAAS,SAAS,SAAS;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAM;QAAS;QAAS;KAAO;IAEnC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,MAAM;QACR,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAuC;;;;;;;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAgE;kDAE5E,8OAAC;wCAAK,WAAU;kDAAkB;;;;;;;;;;;;0CAEpC,8OAAC;gCAAE,WAAU;0CAA2F;;;;;;0CAGxG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;kDAIH,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;8CAK9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;8CAK9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;8CAK9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpD,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}