const https = require('https');
const url = require('url');
const querystring = require('querystring');

// Your OAuth credentials
const CLIENT_ID = '************-3br56ad2jtppd0e1uuq10msihtgof5tb.apps.googleusercontent.com';
const CLIENT_SECRET = '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';
const REDIRECT_URI = 'http://localhost:8080/callback';

// Step 1: Generate the authorization URL
function generateAuthUrl() {
  const authUrl = 'https://accounts.google.com/o/oauth2/auth?' + querystring.stringify({
    client_id: CLIENT_ID,
    redirect_uri: REDIRECT_URI,
    scope: 'https://www.googleapis.com/auth/drive.file',
    response_type: 'code',
    access_type: 'offline',
    prompt: 'consent'
  });
  
  console.log('\n=== STEP 1: Get Authorization Code ===');
  console.log('1. Open this URL in your browser:');
  console.log('\n' + authUrl + '\n');
  console.log('2. After authorizing, you\'ll be redirected to localhost:8080/callback');
  console.log('3. Copy the "code" parameter from the URL and run:');
  console.log('   node get-refresh-token.js YOUR_CODE_HERE\n');
}

// Step 2: Exchange authorization code for tokens
function getTokens(authCode) {
  const postData = querystring.stringify({
    client_id: CLIENT_ID,
    client_secret: CLIENT_SECRET,
    redirect_uri: REDIRECT_URI,
    grant_type: 'authorization_code',
    code: authCode
  });

  const options = {
    hostname: 'oauth2.googleapis.com',
    port: 443,
    path: '/token',
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  console.log('\n=== STEP 2: Exchanging code for tokens ===');
  
  const req = https.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const tokens = JSON.parse(data);
        
        if (tokens.error) {
          console.error('Error:', tokens.error);
          console.error('Description:', tokens.error_description);
          return;
        }
        
        console.log('\n✅ SUCCESS! Here are your tokens:');
        console.log('\n📋 Add this to your .env.local file:');
        console.log(`GOOGLE_REFRESH_TOKEN=${tokens.refresh_token}`);
        
        if (tokens.access_token) {
          console.log('\n🔑 Access Token (expires in 1 hour):');
          console.log(tokens.access_token);
        }
        
        console.log('\n💾 Full response:');
        console.log(JSON.stringify(tokens, null, 2));
        
      } catch (error) {
        console.error('Error parsing response:', error);
        console.error('Raw response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('Request error:', error);
  });

  req.write(postData);
  req.end();
}

// Main execution
const authCode = process.argv[2];

if (!authCode) {
  generateAuthUrl();
} else {
  getTokens(authCode);
}
