{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function middleware(request: NextRequest) {\n  // Skip middleware if environment variables are not set\n  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {\n    return NextResponse.next()\n  }\n\n  let response = NextResponse.next({\n    request: {\n      headers: request.headers,\n    },\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))\n          response = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =>\n            response.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  // Get user profile to check role\n  let userProfile = null\n  if (user) {\n    const { data } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n    userProfile = data\n  }\n\n  const url = request.nextUrl.clone()\n\n  // Redirect authenticated users away from auth pages\n  if (user && (url.pathname === '/login' || url.pathname === '/register')) {\n    url.pathname = '/dashboard'\n    return NextResponse.redirect(url)\n  }\n\n  // Redirect unauthenticated users to login\n  if (!user && (url.pathname.startsWith('/dashboard') || url.pathname.startsWith('/admin'))) {\n    url.pathname = '/login'\n    return NextResponse.redirect(url)\n  }\n\n  // Redirect non-admin users away from admin pages\n  if (user && url.pathname.startsWith('/admin') && userProfile?.role !== 'admin') {\n    url.pathname = '/dashboard'\n    return NextResponse.redirect(url)\n  }\n\n  // Redirect admin users to admin dashboard instead of regular dashboard\n  if (user && userProfile?.role === 'admin' && url.pathname === '/dashboard') {\n    url.pathname = '/admin'\n    return NextResponse.redirect(url)\n  }\n\n  return response\n}\n\nexport const config = {\n  matcher: [\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,uDAAuD;IACvD;;IAIA,IAAI,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC/B,SAAS;YACP,SAAS,QAAQ,OAAO;QAC1B;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,4IAGhC;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBAC7E,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAC3B;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,SAAS,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAEtC;QACF;IACF;IAGF,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,iCAAiC;IACjC,IAAI,cAAc;IAClB,IAAI,MAAM;QACR,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,SACpB,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QACT,cAAc;IAChB;IAEA,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;IAEjC,oDAAoD;IACpD,IAAI,QAAQ,CAAC,IAAI,QAAQ,KAAK,YAAY,IAAI,QAAQ,KAAK,WAAW,GAAG;QACvE,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,0CAA0C;IAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC,iBAAiB,IAAI,QAAQ,CAAC,UAAU,CAAC,SAAS,GAAG;QACzF,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,iDAAiD;IACjD,IAAI,QAAQ,IAAI,QAAQ,CAAC,UAAU,CAAC,aAAa,aAAa,SAAS,SAAS;QAC9E,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,uEAAuE;IACvE,IAAI,QAAQ,aAAa,SAAS,WAAW,IAAI,QAAQ,KAAK,cAAc;QAC1E,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}