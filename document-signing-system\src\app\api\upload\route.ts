import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { GoogleDriveService } from '@/lib/google-drive'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const title = formData.get('title') as string
    const subject = formData.get('subject') as string
    const description = formData.get('description') as string
    const urgency = formData.get('urgency') as string

    if (!file || !title || !subject) {
      return NextResponse.json(
        { error: 'Missing required fields: file, title, subject' },
        { status: 400 }
      )
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Initialize Google Drive service
    const driveService = new GoogleDriveService()

    // Create user folder if it doesn't exist
    const userFolderName = `user_${user.id}`
    const userFolderId = await driveService.createFolder(userFolderName)

    // Create uploaded documents folder
    const uploadedFolderId = await driveService.createFolder('uploaded_documents', userFolderId)

    // Upload file to Google Drive
    const driveFile = await driveService.uploadFile(
      file.name,
      buffer,
      file.type,
      uploadedFolderId
    )

    // Save document record to database
    const { data: document, error: dbError } = await supabase
      .from('documents')
      .insert({
        user_id: user.id,
        title,
        subject,
        description,
        urgency,
        original_filename: file.name,
        original_file_size: file.size,
        original_mime_type: file.type,
        google_drive_file_id: driveFile.id,
        google_drive_folder_id: uploadedFolderId,
        status: 'pending'
      })
      .select()
      .single()

    if (dbError) {
      console.error('Database error:', dbError)
      return NextResponse.json(
        { error: 'Failed to save document record' },
        { status: 500 }
      )
    }

    // Create document history entry
    await supabase
      .from('document_history')
      .insert({
        document_id: document.id,
        new_status: 'pending',
        changed_by: user.id,
        notes: 'Document uploaded'
      })

    return NextResponse.json({
      success: true,
      document,
      driveFile
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
